# 计步器算法改进说明

## 问题分析

原始的计步算法存在以下问题，导致计步数据不够符合人类行走步数：

### 1. 阈值过低
- 原始代码使用固定阈值 `range=1`，过于敏感
- 轻微的手机晃动就被识别为步数
- 缺乏对不同运动强度的适应性

### 2. 缺乏时间间隔控制
- 没有最小时间间隔限制
- 可能导致快速连续计步
- 无法过滤掉非步行运动

### 3. 缺乏重力过滤
- 没有去除重力影响
- 静止时的小幅震动也被计为步数
- 设备方向变化会影响计步准确性

### 4. 峰值检测算法过于简单
- 只检测简单的峰值变化
- 容易误判其他运动为步数
- 缺乏步数合理性验证

### 5. 传感器采样率不当
- 使用 `SENSOR_DELAY_UI` 可能导致过度敏感
- 没有针对计步优化采样频率

## 改进方案

### 1. 动态阈值系统
```java
private static final double MIN_STEP_THRESHOLD = 2.0; // 最小步数阈值
private static final double MAX_STEP_THRESHOLD = 15.0; // 最大步数阈值
private double dynamicThreshold = MIN_STEP_THRESHOLD; // 动态阈值
```

**改进效果：**
- 根据用户运动模式自动调整阈值
- 基于标准差计算动态阈值
- 适应不同的运动强度和设备使用方式

### 2. 时间间隔控制
```java
private static final long MIN_STEP_INTERVAL = 300; // 最小步数间隔(毫秒)
private long lastStepTime = 0; // 上次计步时间
```

**改进效果：**
- 防止过快的连续计步
- 符合人类正常步行频率（每分钟60-200步）
- 过滤掉非步行运动产生的误判

### 3. 重力过滤算法
```java
private double calculateFilteredAcceleration(float x, float y, float z) {
    double totalAcceleration = Math.sqrt(x*x + y*y + z*z);
    double linearAcceleration = Math.abs(totalAcceleration - GRAVITY);
    return linearAcceleration;
}
```

**改进效果：**
- 去除重力分量，只检测线性加速度
- 减少设备方向变化的影响
- 提高静止状态下的准确性

### 4. 智能峰值检测
```java
private boolean detectStep(double acceleration) {
    if (acceleration > dynamicThreshold) {
        if (!isPeakDetected) {
            if (isValidStep(acceleration)) {
                isPeakDetected = true;
                return true;
            }
        }
    } else if (acceleration < dynamicThreshold * 0.5) {
        isPeakDetected = false;
    }
    return false;
}
```

**改进效果：**
- 防止重复计数同一个峰值
- 增加步数有效性验证
- 更准确的峰值状态管理

### 5. 步数有效性验证
```java
private boolean isValidStep(double acceleration) {
    // 检查加速度范围
    if (acceleration < MIN_STEP_THRESHOLD || acceleration > MAX_STEP_THRESHOLD) {
        return false;
    }
    
    // 检查最近的加速度变化模式
    int peakCount = 0;
    for (int i = 0; i < SAMPLE_SIZE - 1; i++) {
        if (recentValues[i] > dynamicThreshold * 0.7) {
            peakCount++;
        }
    }
    
    // 过滤噪声
    return peakCount <= 3;
}
```

**改进效果：**
- 验证加速度是否在合理的步行范围内
- 检查最近的运动模式，过滤噪声
- 防止异常运动被误判为步数

### 6. 优化传感器采样
```java
sensorManager.registerListener(this, sensor, SensorManager.SENSOR_DELAY_GAME);
```

**改进效果：**
- 使用更适合运动检测的采样频率
- 平衡准确性和电池消耗
- 减少过度敏感的问题

### 7. 样本数据管理
```java
private static final int SAMPLE_SIZE = 10;
private double[] recentValues = new double[SAMPLE_SIZE];
private int sampleIndex = 0;
```

**改进效果：**
- 维护最近的加速度样本
- 用于动态阈值计算和模式识别
- 提供更稳定的检测基础

## 算法流程

1. **数据预处理**：去除重力影响，计算线性加速度
2. **样本更新**：维护最近的加速度样本数组
3. **动态阈值**：基于样本统计特性计算动态阈值
4. **峰值检测**：检测是否超过动态阈值
5. **有效性验证**：验证检测到的峰值是否为有效步数
6. **时间过滤**：检查时间间隔，防止过快计步
7. **步数更新**：更新步数并保存数据

## 预期改进效果

1. **准确性提升**：减少误判，提高真实步数识别率
2. **适应性增强**：自动适应不同用户的运动模式
3. **稳定性改善**：减少设备方向和环境因素的影响
4. **电池优化**：合理的采样频率，降低电池消耗
5. **用户体验**：更符合人类行走步数的计步结果

## 测试建议

1. **静止测试**：设备静止时应该不计步或极少计步
2. **正常步行**：正常步行时计步应该准确
3. **快速运动**：跑步时计步应该相对准确
4. **设备晃动**：轻微晃动不应该被计为步数
5. **方向变化**：改变设备方向不应该影响计步准确性

建议在真实设备上进行测试，因为模拟器可能无法准确模拟传感器数据。
