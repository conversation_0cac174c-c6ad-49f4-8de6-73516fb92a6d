# PowerShell脚本用于调整图标大小
Add-Type -AssemblyName System.Drawing

# 源图片路径
$sourceImage = "app\src\main\res\drawable\icon.jpg"

# 检查源文件是否存在
if (-not (Test-Path $sourceImage)) {
    Write-Error "源图片文件不存在: $sourceImage"
    exit 1
}

# 定义不同密度的尺寸
$densities = @{
    "mdpi" = 48
    "hdpi" = 72
    "xhdpi" = 96
    "xxhdpi" = 144
    "xxxhdpi" = 192
}

# 加载源图片
$originalImage = [System.Drawing.Image]::FromFile((Resolve-Path $sourceImage).Path)

Write-Host "原始图片尺寸: $($originalImage.Width) x $($originalImage.Height)"

# 为每个密度创建调整大小的图标
foreach ($density in $densities.Keys) {
    $size = $densities[$density]
    $outputDir = "app\src\main\res\mipmap-$density"
    
    # 确保输出目录存在
    if (-not (Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    }
    
    # 创建新的位图
    $newImage = New-Object System.Drawing.Bitmap($size, $size)
    $graphics = [System.Drawing.Graphics]::FromImage($newImage)
    
    # 设置高质量缩放
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
    $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
    
    # 绘制调整大小的图像
    $graphics.DrawImage($originalImage, 0, 0, $size, $size)
    
    # 保存为PNG格式（推荐用于图标）
    $outputPath = "$outputDir\ic_launcher.png"
    $newImage.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    # 同时创建圆形图标
    $outputPathRound = "$outputDir\ic_launcher_round.png"
    $newImage.Save($outputPathRound, [System.Drawing.Imaging.ImageFormat]::Png)
    
    Write-Host "已创建 $density 密度图标: $outputPath"
    
    # 清理资源
    $graphics.Dispose()
    $newImage.Dispose()
}

# 清理原始图像
$originalImage.Dispose()

Write-Host "所有图标已成功创建！"
