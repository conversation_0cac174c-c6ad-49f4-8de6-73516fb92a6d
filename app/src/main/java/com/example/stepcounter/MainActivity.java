package com.example.stepcounter;

import androidx.appcompat.app.AppCompatActivity;

import android.content.Intent;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;

public class MainActivity extends AppCompatActivity implements View.OnClickListener, SensorEventListener {

    private SensorManager sensorManager;
    private Sensor sensor;
    private TextView textView_step;
    private TextView goalText;
    private TextView progressText;
    private ProgressBar progressBar;
    private Button button_start;
    private Button resetButton;
    private StepDataManager dataManager;

    private int step;
    private boolean processState=false;  //是否已经开始计步

    // 改进的计步算法变量
    private static final double GRAVITY = 9.8; // 重力加速度
    private static final double MIN_STEP_THRESHOLD = 2.0; // 最小步数阈值
    private static final double MAX_STEP_THRESHOLD = 15.0; // 最大步数阈值
    private static final long MIN_STEP_INTERVAL = 300; // 最小步数间隔(毫秒)
    private static final int SAMPLE_SIZE = 10; // 用于计算平均值的样本数量

    private long lastStepTime = 0; // 上次计步时间
    private double[] recentValues = new double[SAMPLE_SIZE]; // 最近的加速度值
    private int sampleIndex = 0; // 当前样本索引
    private boolean isPeakDetected = false; // 是否检测到峰值
    private double dynamicThreshold = MIN_STEP_THRESHOLD; // 动态阈值

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 初始化数据管理器
        dataManager = new StepDataManager(this);

        // 检查是否是新的一天
        if (dataManager.checkAndResetIfNewDay()) {
            step = 0;
        } else {
            step = dataManager.getTodaySteps();
        }



        // 初始化UI组件
        initViews();

        // 初始化传感器
        initSensor();

        // 更新UI显示
        updateUI();
    }

    private void initViews() {
        try {
            textView_step = findViewById(R.id.textview_step);
            goalText = findViewById(R.id.goal_text);
            progressText = findViewById(R.id.progress_text);
            progressBar = findViewById(R.id.progress_bar);
            button_start = findViewById(R.id.button_start);
            resetButton = findViewById(R.id.reset_button);

            if (button_start != null) {
                button_start.setOnClickListener(this);
            }
            if (resetButton != null) {
                resetButton.setOnClickListener(this);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void initSensor() {
        sensorManager=(SensorManager) getSystemService(SENSOR_SERVICE);
        sensor=sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER); //获取传感器，在计步器中需要使用的是加速度传感器

        if (sensor != null) {
            // 使用更合适的采样频率，减少误判
            sensorManager.registerListener(this,sensor,SensorManager.SENSOR_DELAY_GAME);
            // 初始化样本数组
            for (int i = 0; i < SAMPLE_SIZE; i++) {
                recentValues[i] = GRAVITY;
            }
        } else {
            // 如果设备不支持加速度传感器，显示错误信息
            textView_step.setText("设备不支持加速度传感器");
        }
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        if (!processState || event == null || event.values == null || event.values.length < 3) {
            return; // 如果没有开始计步或传感器数据无效，直接返回
        }

        try {
            float[] values = event.values;
            // 计算去除重力影响的加速度幅值
            double acceleration = calculateFilteredAcceleration(values[0], values[1], values[2]);

            // 更新样本数组
            updateSampleArray(acceleration);

            // 动态调整阈值
            updateDynamicThreshold();

            // 检测步数
            if (detectStep(acceleration)) {
                long currentTime = System.currentTimeMillis();

                // 检查时间间隔，避免误判
                if (currentTime - lastStepTime > MIN_STEP_INTERVAL) {
                    step++;
                    lastStepTime = currentTime;
                    if (dataManager != null) {
                        dataManager.saveTodaySteps(step);
                    }
                    updateUI();
                }
            }
        } catch (Exception e) {
            // 捕获任何异常，防止应用崩溃
            e.printStackTrace();
        }
    }

    /**
     * 计算去除重力影响的加速度
     */
    private double calculateFilteredAcceleration(float x, float y, float z) {
        // 计算总加速度
        double totalAcceleration = Math.sqrt(x*x + y*y + z*z);

        // 去除重力分量，得到线性加速度
        double linearAcceleration = Math.abs(totalAcceleration - GRAVITY);

        return linearAcceleration;
    }

    /**
     * 更新样本数组
     */
    private void updateSampleArray(double value) {
        recentValues[sampleIndex] = value;
        sampleIndex = (sampleIndex + 1) % SAMPLE_SIZE;
    }

    /**
     * 动态更新阈值
     */
    private void updateDynamicThreshold() {
        // 计算最近样本的平均值和标准差
        double sum = 0;
        for (double value : recentValues) {
            sum += value;
        }
        double average = sum / SAMPLE_SIZE;

        double variance = 0;
        for (double value : recentValues) {
            variance += Math.pow(value - average, 2);
        }
        double standardDeviation = Math.sqrt(variance / SAMPLE_SIZE);

        // 基于标准差动态调整阈值
        dynamicThreshold = Math.max(MIN_STEP_THRESHOLD,
                          Math.min(MAX_STEP_THRESHOLD, average + 2 * standardDeviation));
    }

    /**
     * 改进的步数检测算法
     */
    private boolean detectStep(double acceleration) {
        // 检查是否超过动态阈值
        if (acceleration > dynamicThreshold) {
            if (!isPeakDetected) {
                // 检测到峰值，但需要验证是否为有效步数
                if (isValidStep(acceleration)) {
                    isPeakDetected = true;
                    return true;
                }
            }
        } else if (acceleration < dynamicThreshold * 0.5) {
            // 重置峰值检测状态
            isPeakDetected = false;
        }

        return false;
    }

    /**
     * 验证是否为有效步数
     */
    private boolean isValidStep(double acceleration) {
        // 检查加速度是否在合理范围内
        if (acceleration < MIN_STEP_THRESHOLD || acceleration > MAX_STEP_THRESHOLD) {
            return false;
        }

        // 检查最近的加速度变化模式
        int peakCount = 0;
        for (int i = 0; i < SAMPLE_SIZE; i++) {
            if (recentValues[i] > dynamicThreshold * 0.7) {
                peakCount++;
            }
        }

        // 如果最近有太多峰值，可能是噪声，不计为步数
        return peakCount <= 3;
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {

    }

    @Override
    public void onClick(View v) {
        if (v == null) return;

        try {
            if (v.getId() == R.id.button_start && button_start != null) {
                if (processState) {
                    button_start.setText("出发！GO！");
                    processState = false;
                } else {
                    button_start.setText("走不动了歇一会~");
                    processState = true;
                }
            } else if (v.getId() == R.id.reset_button) {
                step = 0;
                if (dataManager != null) {
                    dataManager.saveTodaySteps(step);
                }
                updateUI();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (sensorManager != null) {
            sensorManager.unregisterListener(this);
        }
    }



    @Override
    protected void onDestroy(){
        super.onDestroy();
        if (sensorManager != null) {
            sensorManager.unregisterListener(this);
        }
    }

    private double magnitude(float x, float y, float z) {
        double magnitude=0;
        magnitude=Math.sqrt(x*x+y*y+z*z);
        return magnitude;
    }

    private void updateUI() {
        try {
            // 检查UI组件是否已初始化
            if (textView_step == null || goalText == null || progressBar == null ||
                progressText == null || dataManager == null) {
                return;
            }

            // 更新步数显示
            textView_step.setText(String.valueOf(step));

            // 更新目标和进度显示
            int dailyGoal = dataManager.getDailyGoal();
            goalText.setText("目标: " + String.format("%,d", dailyGoal) + " 步");

            // 更新进度条
            float progress = dataManager.getGoalCompletionRate();
            progressBar.setProgress((int)(progress * 100));

            // 更新进度文本
            if (step >= dailyGoal) {
                progressText.setText("🎉 恭喜完成今日目标！");
                progressText.setTextColor(0xFF4CAF50); // 绿色
            } else {
                int remaining = dailyGoal - step;
                progressText.setText("还需 " + String.format("%,d", remaining) + " 步完成目标");
                progressText.setTextColor(0xFFFF9800); // 橙色
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.action_history) {
            Intent intent = new Intent(this, HistoryActivity.class);
            startActivity(intent);
            return true;
        } else if (id == R.id.action_settings) {
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (sensor != null && sensorManager != null) {
            sensorManager.registerListener(this, sensor, SensorManager.SENSOR_DELAY_GAME);
        }
        // 重新加载数据以防设置发生变化
        updateUI();
    }
}