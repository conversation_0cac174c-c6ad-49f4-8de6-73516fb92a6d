package com.example.stepcounter;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 简单的单元测试来验证MainActivity的基本功能
 */
public class MainActivityTest {

    @Test
    public void testMagnitudeCalculation() {
        MainActivity activity = new MainActivity();
        
        // 使用反射来测试私有方法magnitude
        try {
            java.lang.reflect.Method magnitudeMethod = MainActivity.class.getDeclaredMethod("magnitude", float.class, float.class, float.class);
            magnitudeMethod.setAccessible(true);
            
            // 测试基本的magnitude计算
            double result = (Double) magnitudeMethod.invoke(activity, 3.0f, 4.0f, 0.0f);
            assertEquals(5.0, result, 0.001);
            
            // 测试零向量
            result = (Double) magnitudeMethod.invoke(activity, 0.0f, 0.0f, 0.0f);
            assertEquals(0.0, result, 0.001);
            
            // 测试三维向量
            result = (Double) magnitudeMethod.invoke(activity, 1.0f, 1.0f, 1.0f);
            assertEquals(Math.sqrt(3), result, 0.001);
            
        } catch (Exception e) {
            fail("测试magnitude方法时发生异常: " + e.getMessage());
        }
    }

    @Test
    public void testFilteredAccelerationCalculation() {
        MainActivity activity = new MainActivity();

        // 使用反射来测试私有方法calculateFilteredAcceleration
        try {
            java.lang.reflect.Method filteredAccelMethod = MainActivity.class.getDeclaredMethod("calculateFilteredAcceleration", float.class, float.class, float.class);
            filteredAccelMethod.setAccessible(true);

            // 测试静止状态（接近重力加速度）
            double result = (Double) filteredAccelMethod.invoke(activity, 0.0f, 0.0f, 9.8f);
            assertEquals(0.0, result, 0.1); // 静止时应该接近0

            // 测试运动状态
            result = (Double) filteredAccelMethod.invoke(activity, 2.0f, 3.0f, 12.0f);
            assertTrue("运动时应该有明显的线性加速度", result > 1.0);

            // 测试负重力方向
            result = (Double) filteredAccelMethod.invoke(activity, 0.0f, 0.0f, -9.8f);
            assertEquals(0.0, result, 0.1);

        } catch (Exception e) {
            fail("测试calculateFilteredAcceleration方法时发生异常: " + e.getMessage());
        }
    }

    @Test
    public void testValidStepDetection() {
        MainActivity activity = new MainActivity();

        // 使用反射来测试私有方法isValidStep
        try {
            java.lang.reflect.Method isValidStepMethod = MainActivity.class.getDeclaredMethod("isValidStep", double.class);
            isValidStepMethod.setAccessible(true);

            // 测试有效步数范围
            boolean result = (Boolean) isValidStepMethod.invoke(activity, 3.0);
            assertTrue("正常步数应该被识别为有效", result);

            // 测试过小的加速度
            result = (Boolean) isValidStepMethod.invoke(activity, 1.0);
            assertFalse("过小的加速度不应该被识别为步数", result);

            // 测试过大的加速度
            result = (Boolean) isValidStepMethod.invoke(activity, 20.0);
            assertFalse("过大的加速度不应该被识别为步数", result);

        } catch (Exception e) {
            fail("测试isValidStep方法时发生异常: " + e.getMessage());
        }
    }
}
